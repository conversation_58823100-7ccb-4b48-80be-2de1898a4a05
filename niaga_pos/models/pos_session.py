# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import logging

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError, except_orm
from odoo.tools import float_is_zero

_logger = logging.getLogger(__name__)


class PosSession(models.Model):
    _inherit = 'pos.session'

    name = fields.Char(index=True)
    start_at = fields.Datetime(index=True)
    stop_at = fields.Datetime(index=True)
    operating_unit_id = fields.Many2one('operating.unit', required=True, index=True)
    account_move_id = fields.Many2one('account.move', string='Journal Entry')
    account_move_non_pkp_id = fields.Many2one('account.move', string='Journal Entry 1')

    @api.multi
    def _get_invalid_pickings(self):
        self.ensure_one()
        pickings = self.order_ids.mapped('picking_id').filtered(
            lambda p: p.state != 'done'
        )
        return_pickings = self.order_ids.mapped('picking_return_id').filtered(
            lambda p: p.state != 'done'
        )
        return pickings + return_pickings

    def _compute_picking_count(self):
        for session in self:
            pickings = session._get_invalid_pickings()
            session.picking_count = len(pickings)

    @api.multi
    def action_stock_picking(self):
        self.ensure_one()
        pickings = self._get_invalid_pickings()
        action_picking = self.env.ref('stock.action_picking_tree_ready')
        action = action_picking.read()[0]
        action['context'] = {}
        action['domain'] = [('id', 'in', pickings.ids)]
        return action

    @api.constrains('user_id', 'state')
    def _check_unicity(self):
        for session in self:
            if (
                self.search_count(
                    [
                        ('state', 'not in', ('closed', 'closing_control')),
                        ('user_id', '=', session.user_id.id),
                        ('rescue', '=', False),
                    ]
                )
                > 1
            ):
                raise ValidationError(
                    _('You cannot create two active sessionswith the same responsible!')
                )

    @api.constrains('config_id')
    def _check_pos_config(self):
        for session in self:
            if (
                self.search_count(
                    [
                        ('state', '!=', 'closed'),
                        ('config_id', '=', session.config_id.id),
                        ('rescue', '=', False),
                    ]
                )
                > 1
            ):
                raise ValidationError(
                    _('Another session is already opened for this point of sale.')
                )

    @api.model
    def create(self, values):
        if not values.get('operating_unit_id', False) and self._context.get(
            'default_operating_unit_id', False
        ):
            values['operating_unit_id'] = self._context.get('default_operating_unit_id')

        result = super().create(values)
        sequence_id = result.config_id.sequence_session_id
        if not sequence_id:
            result.config_id.generate_session_sequence()

        new_name = result.config_id.sequence_session_id.next_by_id()
        result.name = new_name
        result.statement_ids.write(
            {'name': new_name, 'operating_unit_id': result.operating_unit_id.id}
        )
        return result

    @api.multi
    def action_pos_session_close(self):
        for session in self:
            if session.picking_count > 0:
                raise UserError(
                    _(
                        'There is some picking data that has not been validated, '
                        'please wait or contact your administrator!'
                    )
                )
        return super().action_pos_session_close()

    def _moves_fast_reconcile(self, amls):
        company_ids = set(amls.mapped('company_id.id'))
        if len(company_ids) > 1:
            raise UserError(
                _('To reconcile company should be the same for all entries!')
            )
        all_accounts = amls.mapped('account_id')
        if len(set(all_accounts)) > 1:
            raise UserError(_('Entries are not of the same account!'))
        if not (
            all_accounts[0].reconcile or all_accounts[0].internal_type == 'liquidity'
        ):
            raise UserError(
                _('The account %s (%s) is not marked as reconciliable !')
                % (all_accounts[0].name, all_accounts[0].code)
            )

        debit_amls = amls.filtered(lambda aml: aml.debit > 0 and not aml.reconciled)
        credit_amls = amls.filtered(lambda aml: aml.credit > 0 and not aml.reconciled)

        company = debit_amls and debit_amls[0].company_id
        company_currency_id = company.currency_id

        sum_debit = sum(debit_amls.mapped('debit'))
        sum_credit = sum(credit_amls.mapped('credit'))
        # If reconciliation is not total or that all moves don't have same currency,
        # it won't be reconciled at all
        if not float_is_zero(
            sum_debit - sum_credit, precision_rounding=company_currency_id.rounding
        ):
            return

        if len(set([a.currency_id for a in amls])) > 1:
            return

        currency_id = debit_amls and debit_amls[0].currency_id
        if currency_id.id:
            currency_id = currency_id.id
        else:
            currency_id = None

        full_reconcile = self.env['account.full.reconcile'].create({})
        partial_reconcile_vals = []

        while True:
            if not debit_amls.ids or not credit_amls.ids:
                break

            debit = debit_amls[0]
            credit = credit_amls[0]
            amount = min(debit.amount_residual, credit.amount_residual)
            amount_currency = min(debit.amount_currency, credit.amount_currency)

            vals = {
                'full_reconcile_id': full_reconcile.id,
                'debit_move_id': debit.id,
                'credit_move_id': credit.id,
                'amount': amount,
                'amount_currency': amount_currency,
                'currency_id': currency_id,
                'company_id': company.id,
            }

            if float_is_zero(
                debit.amount_residual - amount,
                precision_rounding=company_currency_id.rounding,
            ):
                debit_amls = debit_amls[1:]
            else:
                debit_amls[0].amount_residual -= amount
                debit_amls[0].amount_residual_currency -= amount_currency
            if float_is_zero(
                credit.amount_residual - amount,
                precision_rounding=company_currency_id.rounding,
            ):
                credit_amls = credit_amls[1:]
            else:
                credit_amls[0].amount_residual -= amount
                credit_amls[0].amount_residual_currency -= amount_currency

            partial_reconcile_vals.append(vals)

        self.env['account.partial.reconcile']._create_raw(partial_reconcile_vals)
        # update account_move_line
        move_line_vals = [
            {
                'id': aml.id,
                'reconciled': True,
                'amount_residual': 0,
                'amount_residual_currency': 0,
                'full_reconcile_id': full_reconcile.id,
            }
            for aml in amls
        ]
        self.env['account.move.line']._write_raw(move_line_vals)

    def _reconcile_payments_new(self, account_move, statement_ids):
        journal_entry_ids = statement_ids.mapped('line_ids').mapped('journal_entry_ids')

        amls = journal_entry_ids | account_move.line_ids
        amls = amls.filtered(lambda line: line.account_id.internal_type == 'receivable')
        amls = amls.filtered(lambda line: not line.reconciled)

        try:
            self._moves_fast_reconcile(amls)
            self.invalidate_cache()
        except except_orm:
            raise
        except Exception:
            _logger.warning(
                _(
                    'Error when trying to fast-reconcile POS Session move lines'
                    'falling back to standard code'
                )
            )

            try:
                amls.with_context(skip_tax_cash_basis_entry=True).reconcile()
            except Exception:
                _logger.exception(
                    f'Reconciliation did not work for session {self.name}'
                )

            partial_reconcile = self.env['account.partial.reconcile'].search(
                [
                    '|',
                    ('credit_move_id.move_id', '=', account_move.id),
                    ('debit_move_id.move_id', '=', account_move.id),
                ],
                limit=1,
            )
            if partial_reconcile:
                partial_reconcile.create_tax_cash_basis_entry(
                    account_move.line_ids._get_matched_percentage()
                )

    def _confirm_orders(self):
        for session in self:
            ctx = {'default_operating_unit_id': session.operating_unit_id.id}
            orders = session.order_ids.filtered(lambda order: order.state == 'paid')
            orders.with_context(ctx)._create_account_moves(session)
            for order in session.order_ids.filtered(
                lambda o: o.state not in ['done', 'invoiced']
            ):
                if order.state not in ('paid'):
                    raise UserError(
                        _(
                            'You cannot confirm all orders of this session,',
                            "because they have not the 'paid' status.\n"
                            '{} is in state {}, total amount: {}, paid: {}',
                        ).format(
                            order.pos_reference or order.name,
                            dict(
                                order._fields['state']._description_selection(self.env)
                            ).get(order.state),
                            order.amount_total,
                            order.amount_paid,
                        )
                    )
                order.action_pos_order_done()

            if session.account_move_id:
                statement_ids = session.statement_ids.filtered(
                    lambda line: not line.journal_id.is_non_pkp
                )
                session._reconcile_payments_new(session.account_move_id, statement_ids)

            if session.account_move_non_pkp_id:
                statement_ids = session.statement_ids.filtered(
                    lambda line: line.journal_id.is_non_pkp
                )
                session._reconcile_payments_new(
                    session.account_move_non_pkp_id, statement_ids
                )

            session.create_bank_intransit()

    @api.multi
    def create_bank_intransit(self):
        self.ensure_one()
        ctx = {'default_operating_unit_id': self.operating_unit_id.id}
        Order = self.env['pos.order'].with_context(ctx)
        Payment = self.env['account.payment'].with_context(ctx)
        for statement in self.statement_ids.filtered(
            lambda s: s.journal_id.type == 'bank'
            and s.journal_id.is_edc
            and s.total_entry_encoding
        ):
            journal_id = statement.journal_id
            company_id = self.config_id.company_id
            transfer_journal = journal_id.transfer_journal_id

            if not transfer_journal:
                raise UserError(
                    _(
                        f'Transfer Journal EDC account not found \
                    in this Bank {journal_id.name} configuration.'
                    )
                )

            lang_code = self.env.user.lang or 'en_US'
            lang_id = self.env['res.lang']._lang_get(lang_code)

            start_date = fields.Datetime.context_timestamp(
                self, fields.Datetime.from_string(self.start_at)
            )
            start_date = start_date.date().strftime(lang_id.date_format)
            move_name = f'{journal_id.name} {start_date}'.upper()

            move = Order._create_account_move(
                self.start_at,
                move_name,
                transfer_journal.id,
                company_id.id,
            )

            payment_methods = journal_id.inbound_payment_method_ids
            currency = (
                statement.journal_id.currency_id or statement.company_id.currency_id
            )
            payment_method_id = payment_methods and payment_methods[0].id or False
            payment_id = Payment.create(
                {
                    'payment_method_id': payment_method_id,
                    'payment_type': 'transfer',
                    'journal_id': journal_id.id,
                    'destination_journal_id': transfer_journal.id,
                    'payment_date': move.date,
                    'state': 'reconciled',
                    'currency_id': currency.id,
                    'amount': abs(statement.total_entry_encoding),
                    'communication': _('Bank In-Transit MDR'),
                    'name': statement.name or _('Bank Statement %s') % move.date,
                }
            )

            amount_transaction = statement.total_entry_encoding
            amount_intransit = amount_transaction * (1 - journal_id.mdr_amount / 100)
            amount_mdr = amount_transaction - amount_intransit
            move_lines = []

            mdr_account_id = journal_id.mdr_debit_account_id.id
            intransit_account = transfer_journal.default_debit_account_id
            edc_account_id = journal_id.default_credit_account_id.id

            if amount_transaction < 0:
                mdr_account_id = journal_id.mdr_credit_account_id.id
                intransit_account = transfer_journal.default_credit_account_id
                edc_account_id = journal_id.default_debit_account_id.id

            mdr_name = f'MDR {journal_id.name} {journal_id.mdr_amount}%'.upper()

            mdr_line = {
                'name': _(mdr_name),
                'account_id': mdr_account_id,
                'credit': abs(amount_mdr) if amount_mdr < 0 else 0,
                'debit': amount_mdr if amount_mdr > 0 else 0,
                'payment_id': payment_id.id,
            }
            move_lines.append((0, 0, mdr_line))

            intransit_name = f'TRANSAKSI {journal_id.name}'.upper()

            intransit_line = {
                'name': _(intransit_name),
                'account_id': intransit_account.id,
                'credit': abs(amount_intransit) if amount_intransit < 0 else 0,
                'debit': amount_intransit if amount_intransit > 0 else 0,
                'payment_id': payment_id.id,
            }
            move_lines.append((0, 0, intransit_line))

            edc_name = intransit_account.name.upper()

            edc_line = {
                'name': _(edc_name),
                'account_id': edc_account_id,
                'credit': amount_transaction if amount_transaction > 0 else 0,
                'debit': abs(amount_transaction) if amount_transaction < 0 else 0,
                'payment_id': payment_id.id,
            }
            move_lines.append((0, 0, edc_line))

            move.line_ids = move_lines
            move.post()
