# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import math
from collections import defaultdict
from datetime import date, datetime, time

from dateutil.relativedelta import relativedelta
from pytz import timezone

from odoo import _, api, fields, models
from odoo.exceptions import ValidationError
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT, split_every

import odoo.addons.decimal_precision as dp


class Orderpoint(models.Model):
    _inherit = 'stock.warehouse.orderpoint'

    @api.model
    def default_get(self, fields):
        res = super(Orderpoint, self).default_get(fields)
        if res.get('location_id'):
            res.pop('location_id')
        return res

    operating_unit_id = fields.Many2one(
        'operating.unit',
        string='Operating Unit',
        index=True,
        ondelete='cascade',
        required=True,
        default=lambda self: self.env.user.default_operating_unit_id.id,
    )
    qty_available = fields.Float(
        string='Current Stock on Hand',
        digits=dp.get_precision('Product Unit of Measure'),
        copy=False,
        default=0.0,
    )
    qty_sales_week = fields.Float(
        string='Total Sales Prev Week',
        digits=dp.get_precision('Product Unit of Measure'),
        copy=False,
        default=0.0,
    )
    qty_sales_month = fields.Float(
        string='Total Sales Prev Month',
        digits=dp.get_precision('Product Unit of Measure'),
        copy=False,
        default=0.0,
    )

    @api.model_cr
    def init(self):
        self._cr.execute(
            'SELECT indexname FROM pg_indexes WHERE indexname = %s',
            ('stock_warehouse_orderpoint_id_product_id_idx',),
        )
        if not self._cr.fetchone():
            self._cr.execute(
                """CREATE INDEX
                    stock_warehouse_orderpoint_id_product_id_idx
                    ON stock_warehouse_orderpoint (id, product_id)"""
            )
        self._cr.execute(
            'SELECT indexname FROM pg_indexes WHERE indexname = %s',
            ('stock_warehouse_orderpoint_id_product_id_operating_unit_id_idx',),
        )
        if not self._cr.fetchone():
            self._cr.execute(
                """CREATE INDEX
                    stock_warehouse_orderpoint_id_product_id_operating_unit_id_idx
                    ON stock_warehouse_orderpoint (id, product_id, operating_unit_id)"""
            )

    @api.onchange('operating_unit_id')
    def onchange_operating_unit_id(self):
        warehouse_id = self.operating_unit_id.warehouse_id or self.env[
            'stock.warehouse'
        ].search([('operating_unit_id', '=', self.operating_unit_id.id)], limit=1)
        self.warehouse_id = warehouse_id.id
        self.location_id = warehouse_id.view_location_id.id

    @api.onchange('warehouse_id')
    def onchange_warehouse_id(self):
        self.location_id = self.warehouse_id.view_location_id.id
        self.operating_unit_id = self.warehouse_id.operating_unit_id

    @api.one
    @api.constrains('product_id', 'operating_unit_id', 'warehouse_id', 'location_id')
    def _check_unqiue_orderpoint(self):
        if (
            self.search_count(
                [
                    ('product_id', '=', self.product_id.id),
                    ('operating_unit_id', '=', self.operating_unit_id.id),
                    ('warehouse_id', '=', self.warehouse_id.id),
                    ('location_id', '=', self.location_id.id),
                ]
            )
            > 1
        ):
            raise ValidationError(_('Orderpoint must be unique!'))

    @api.model
    def _check_purchase_lines(
        self,
        operating_unit_id,
        product,
        seller,
        qty_to_order,
        price_unit,
        order_line_limit,
    ):
        PurchaseOrder = self.env['purchase.order']
        partner_id = seller.name.id
        domain = [
            ('partner_id', '=', partner_id),
            ('state', '=', 'draft'),
            ('operating_unit_id', '=', operating_unit_id),
        ]

        result = False
        order_line = self.env['purchase.order.line'].search(
            domain + [('product_id', '=', product.id)], limit=1
        )

        if order_line:
            result = True
            if order_line.product_qty != qty_to_order:
                order_line.product_qty = qty_to_order

        else:
            purchase_orders = PurchaseOrder.search(domain)
            for order in purchase_orders.filtered(
                lambda po: len(po.order_line) < order_line_limit
            ):
                line_val = self._prepare_purchase_line_vals(
                    product, seller, qty_to_order, price_unit
                )
                order.order_line = [(0, 0, line_val)]
                result = True
                break

        return result

    @api.model
    def _prepare_purchase_line_vals(self, product, seller, qty_to_order, price_unit):
        partner = seller.name
        product_lang = product.with_context(lang=partner.lang, partner_id=partner.id)
        name = product_lang.display_name
        if product_lang.description_purchase:
            name += '\n' + product_lang.description_purchase

        fpos = partner.property_account_position_id
        taxes = product.supplier_taxes_id
        taxes_id = fpos.map_tax(taxes) if fpos else taxes

        vals = {
            'product_id': product.id,
            'name': name,
            'date_planned': datetime.now(),
            'product_uom': product.uom_po_id.id,
            'product_qty': qty_to_order,
            'price_unit': price_unit,
            'taxes_id': [(6, 0, taxes_id.ids)],
            'discount_first': seller.discount_first,
            'discount_second': seller.discount_second,
            'discount_third': seller.discount_third,
            'discount_amount': seller.discount_amount,
        }
        return vals

    @api.model
    def _get_orderpoints(self, reordering):
        params = [
            reordering.schedule_day,
            int(reordering.type_of_week),
            reordering.visit_freq_in_weeks,
            reordering.operating_unit_id.id,
        ]

        subquery = """
            SELECT
                DISTINCT ON (pp.id) pp.id as product_id,
                ps.id as seller_id,
                ps.product_tmpl_id as product_tmpl_id
            FROM product_supplierinfo ps
            JOIN res_partner rp ON rp.id = ps.name
            JOIN product_product pp ON pp.product_tmpl_id = ps.product_tmpl_id
            WHERE
                rp.schedule_day = %s
                AND rp.type_of_week = %s
                AND rp.visit_freq_in_weeks = %s
                AND ps.operating_unit_id = %s
        """
        if reordering.partner_ids:
            subquery += '\nAND ps.name in %s'
            params.append(
                tuple(reordering.partner_ids.ids),
            )

        subquery += '\nORDER BY pp.id, ps.sequence ASC'
        params.append(reordering.operating_unit_id.id)

        query = f"""
            SELECT wo.id as orderpoint_id, sub.seller_id
            FROM stock_warehouse_orderpoint wo
            JOIN (
                {subquery}
            ) AS sub ON sub.product_id = wo.product_id
            LEFT JOIN product_template pt ON pt.id = sub.product_tmpl_id
            WHERE wo.operating_unit_id = %s
            ORDER BY pt.name ASC
        """

        self._cr.execute(query, tuple(params))
        result = self._cr.dictfetchall()

        orderpoint_ids = [r.get('orderpoint_id') for r in result]
        orderpoints = self.browse(orderpoint_ids)

        seller_ids = [r.get('seller_id') for r in result]
        sellers = self.env['product.supplierinfo'].browse(seller_ids)
        sellers_by_product = {
            seller.product_tmpl_id.product_variant_id.id: seller for seller in sellers
        }

        return orderpoints, sellers_by_product

    def convert_date_to_datetime(self, input_date):
        default_datetime = datetime.combine(input_date, time())
        user_tz = timezone(self.env.user.tz or 'Asia/Jakarta')
        local_datetime = user_tz.localize(default_datetime)
        utc_datetime = local_datetime.astimezone(timezone('UTC'))
        return utc_datetime

    def _get_pos_sessions(self, operating_unit_id, start_date, total_days_to_get):
        Session = self.env['pos.session']
        total_sales_days = 0
        sessions = Session
        first_session = Session.search(
            [('operating_unit_id', '=', operating_unit_id)], limit=1, order='id ASC'
        )
        if not first_session:
            return sessions

        while total_sales_days != total_days_to_get:
            session_start_date = self.convert_date_to_datetime(start_date).strftime(
                DEFAULT_SERVER_DATETIME_FORMAT
            )
            session_end_date = self.convert_date_to_datetime(
                start_date + relativedelta(days=1)
            ).strftime(DEFAULT_SERVER_DATETIME_FORMAT)
            session = Session.search(
                [
                    ('operating_unit_id', '=', operating_unit_id),
                    ('start_at', '>=', session_start_date),
                    ('start_at', '<=', session_end_date),
                ]
            )
            if session:
                total_sales_days += 1
                sessions |= session

            if fields.Datetime.from_string(
                first_session.start_at
            ) > fields.Datetime.from_string(session_start_date):
                break

            start_date -= relativedelta(days=1)

        return sessions

    @api.model
    def _get_grouped_total_sales(self, product_ids, session_ids):
        if not product_ids or not session_ids:
            return {}

        self = self.sudo()
        query = """
            SELECT
                ol.product_id AS product_id,
                SUM(CASE
                    WHEN ol.product_uom != t.uom_id
                    THEN ol.qty * uom.factor
                    ELSE ol.qty
                END) AS total_order_qty
            FROM pos_order_line AS ol
            JOIN product_product AS p ON ol.product_id = p.id
            JOIN product_template AS t on p.product_tmpl_id = t.id
            JOIN pos_order AS o ON ol.order_id = o.id
            LEFT JOIN product_uom AS uom ON t.uom_id = uom.id
            WHERE ol.product_id in %s
            AND o.session_id in %s
            AND o.state IN ('paid', 'done')
            GROUP BY ol.product_id
        """
        params = (tuple(product_ids), tuple(session_ids))

        self._cr.execute(query, params)
        result = self.env.cr.fetchall()
        return result

    @api.multi
    def _get_grouped_pos_sales(
        self, operating_unit_id, week_interval, grouped_product_by_leadtime
    ):
        today = date.today()
        data_qty_prev_week = {}
        data_qty_prev_month = {}
        for lead_time, product_ids in grouped_product_by_leadtime.items():
            start_prev_week = today - relativedelta(days=1)
            total_days = (week_interval * 7) + lead_time

            prev_week_sessions = self._get_pos_sessions(
                operating_unit_id, start_prev_week, total_days
            )
            data_qty_prev_week.update(
                self._get_grouped_total_sales(product_ids, prev_week_sessions.ids)
            )

            if week_interval <= 2:
                start_prev_month = today - relativedelta(months=1)
                prev_month_sessions = self._get_pos_sessions(
                    operating_unit_id, start_prev_month, total_days
                )
                data_qty_prev_month.update(
                    self._get_grouped_total_sales(product_ids, prev_month_sessions.ids)
                )

        return data_qty_prev_week, data_qty_prev_month

    def _get_grouped_product_by_leadtime(self, sellers_by_product, orderpoints):
        grouped_data = defaultdict(list)
        for orderpoint in orderpoints:
            product_id = orderpoint.product_id.id
            seller = sellers_by_product.get(product_id)
            if not seller:
                continue
            lead_time = orderpoint.lead_days if seller.delay < 1 else seller.delay
            grouped_data[lead_time].append(product_id)

        return grouped_data

    @api.model
    def _prepare_purchase_data(self, reordering):
        """Prepare all data needed for purchase value calculation."""
        orderpoints, sellers_by_product = self._get_orderpoints(reordering)
        operating_unit_id = reordering.operating_unit_id.id
        week_interval = reordering.visit_freq_in_weeks

        if not orderpoints or not sellers_by_product:
            return None

        products = orderpoints.mapped('product_id')
        data_product_ranks = products._get_product_ranks(operating_unit_id)
        data_qty_availables = products.with_context(
            location=reordering.operating_unit_id.warehouse_id.view_location_id.id
        ).get_qty_availables()

        grouped_product_by_leadtime = self._get_grouped_product_by_leadtime(
            sellers_by_product, orderpoints
        )
        data_qty_prev_week, data_qty_prev_month = self._get_grouped_pos_sales(
            operating_unit_id, week_interval, grouped_product_by_leadtime
        )

        return {
            'orderpoints': orderpoints,
            'sellers_by_product': sellers_by_product,
            'operating_unit_id': operating_unit_id,
            'week_interval': week_interval,
            'data_product_ranks': data_product_ranks,
            'data_qty_availables': data_qty_availables,
            'data_qty_prev_week': data_qty_prev_week,
            'data_qty_prev_month': data_qty_prev_month,
        }

    @api.model
    def _calculate_order_quantity(self, orderpoint, data_dict):
        """Calculate the order quantity for a given orderpoint."""
        ProductRank = self.env['product.rank']
        product = orderpoint.product_id

        qty_prev_week = data_dict['data_qty_prev_week'].get(product.id, 0)
        qty_prev_month = data_dict['data_qty_prev_month'].get(product.id, 0)
        min_qty = orderpoint.product_min_qty
        qty_available = data_dict['data_qty_availables'].get(product.id, 0)
        product_rank_id = data_dict['data_product_ranks'].get(product.id, [False])[0]
        week_interval = data_dict['week_interval']

        sales_max_qty = max(qty_prev_week, qty_prev_month)

        if min_qty > qty_prev_week and min_qty > qty_prev_month and qty_available <= 0:
            return min_qty
        elif not product_rank_id:
            return sales_max_qty
        else:
            product_rank = ProductRank.browse(product_rank_id)
            rank_min_qty, rank_max_qty = product_rank._get_min_max()

            qty_rank_week_in_from = round(rank_min_qty / 30 * 7 * week_interval)
            qty_rank_week_in_to = round(rank_max_qty / 30 * 7 * week_interval)

            if product_rank.key == 'A' and sales_max_qty >= qty_rank_week_in_from:
                return sales_max_qty
            elif sales_max_qty < qty_rank_week_in_from:
                return qty_rank_week_in_from
            elif product_rank.key != 'A':
                if sales_max_qty > rank_max_qty:
                    return qty_rank_week_in_to
                elif sales_max_qty >= rank_min_qty:
                    if sales_max_qty > rank_max_qty:
                        return qty_rank_week_in_to
                    else:
                        return sales_max_qty
        return 0

    @api.model
    def _process_orderpoint_for_purchase(
        self,
        orderpoint,
        seller,
        data_dict,
        reordering,
        order_line_limit,
        purchase_values,
        orderpoints_to_updates,
    ):
        """Process a single orderpoint for purchase creation."""
        today = date.today()
        product = orderpoint.product_id
        partner = seller.name
        lead_time = orderpoint.lead_days if seller.delay < 1 else seller.delay
        operating_unit_id = data_dict['operating_unit_id']

        qty_prev_week = data_dict['data_qty_prev_week'].get(product.id, 0)
        qty_prev_month = data_dict['data_qty_prev_month'].get(product.id, 0)
        qty_available = data_dict['data_qty_availables'].get(product.id, 0)

        # Calculate order quantity
        qty_order = self._calculate_order_quantity(orderpoint, data_dict)

        orderpoint_vals = {'product_min_qty': qty_order}

        # Apply buffer percentage
        qty_order *= (100 + reordering.buffer_percentage) / 100
        orderpoint_vals['product_max_qty'] = qty_order

        # Adjust for available quantity
        qty_order -= qty_available
        qty_order = math.ceil(qty_order / product.uom_po_id.factor_inv)
        if qty_order < 1:
            return

        # Check if purchase line already exists
        is_purchase_line_exist = self._check_purchase_lines(
            operating_unit_id,
            product,
            seller,
            qty_order,
            seller.price,
            order_line_limit,
        )
        if is_purchase_line_exist:
            return

        # Update orderpoint values
        orderpoint_vals.update(
            {
                'qty_available': qty_available,
                'qty_sales_week': qty_prev_week,
                'qty_sales_month': qty_prev_month,
            }
        )

        if orderpoint_vals:
            orderpoints_to_updates.append(dict(orderpoint_vals, id=orderpoint.id))

        # Create purchase line
        date_planned = today + relativedelta(days=lead_time)
        purchase_line_vals = dict(
            self._prepare_purchase_line_vals(product, seller, qty_order, seller.price),
            orderpoint_id=orderpoint.id,
            date_planned=date_planned,
        )

        # Add to purchase values
        partner_per_ou = (partner.id, operating_unit_id)
        if partner_per_ou not in purchase_values:
            purchase_values[partner_per_ou] = {
                'partner_id': partner.id,
                'date_order': today,
                'date_planned': date_planned,
                'operating_unit_id': operating_unit_id,
                'order_line': [(0, 0, purchase_line_vals)],
                'picking_type_id': orderpoint.warehouse_id.in_type_id.id,
                'state': 'draft',
            }
        else:
            purchase_values[partner_per_ou]['order_line'].append(
                (0, 0, purchase_line_vals)
            )

    @api.model
    def _prepare_purchase_vals(self, reordering, order_line_limit):
        """Prepare purchase values for the given reordering."""
        self = self.sudo()
        purchase_values = {}
        orderpoints_to_updates = []

        # Prepare all required data
        data_dict = self._prepare_purchase_data(reordering)
        if not data_dict:
            return purchase_values

        # Process each orderpoint
        for orderpoint in data_dict['orderpoints']:
            seller = data_dict['sellers_by_product'].get(orderpoint.product_id.id)
            if not seller:
                continue

            self._process_orderpoint_for_purchase(
                orderpoint,
                seller,
                data_dict,
                reordering,
                order_line_limit,
                purchase_values,
                orderpoints_to_updates,
            )

        # Update orderpoints if needed
        if orderpoints_to_updates:
            self._write_raw(orderpoints_to_updates)
            self.invalidate_cache()

        return purchase_values

    @api.model
    def _create_purchases(self, purchase_values, order_line_limit):
        PurchaseOrder = self.env['purchase.order']
        purchases = PurchaseOrder
        for vals in purchase_values.values():
            purchase_vals = vals.copy()
            line_vals = purchase_vals.get('order_line')
            for order_line in split_every(order_line_limit, line_vals):
                purchase = PurchaseOrder.create(
                    dict(purchase_vals, order_line=order_line)
                )
                purchase.onchange_partner_id()
                purchase.onchange_partner_id_warning()
                purchases |= purchase
        return purchases

    @api.model
    def _generate_purchases(self, reordering):
        get_param = self.env['ir.config_parameter'].sudo().get_param
        order_line_limit = get_param('purchase.order_line_limit', 30)

        purchase_values = self._prepare_purchase_vals(reordering, order_line_limit)
        purchases = self._create_purchases(purchase_values, order_line_limit)
        return purchases
